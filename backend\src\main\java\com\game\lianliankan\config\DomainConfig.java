package com.game.lianliankan.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 域名配置类
 * 用于统一管理前后端域名配置
 */
@Component
@ConfigurationProperties(prefix = "app")
public class DomainConfig {
    
    private Domain domain = new Domain();
    private String environment = "dev";
    
    public Domain getDomain() {
        return domain;
    }
    
    public void setDomain(Domain domain) {
        this.domain = domain;
    }
    
    public String getEnvironment() {
        return environment;
    }
    
    public void setEnvironment(String environment) {
        this.environment = environment;
    }
    
    /**
     * 获取当前环境的前端域名
     */
    public String getFrontendDomain() {
        if ("prod".equals(environment)) {
            return domain.getFrontend().getProd();
        }
        return domain.getFrontend().getDev();
    }
    
    /**
     * 获取当前环境的后端域名
     */
    public String getBackendDomain() {
        if ("prod".equals(environment)) {
            return domain.getBackend().getProd();
        }
        return domain.getBackend().getDev();
    }
    
    public static class Domain {
        private Frontend frontend = new Frontend();
        private Backend backend = new Backend();
        
        public Frontend getFrontend() {
            return frontend;
        }
        
        public void setFrontend(Frontend frontend) {
            this.frontend = frontend;
        }
        
        public Backend getBackend() {
            return backend;
        }
        
        public void setBackend(Backend backend) {
            this.backend = backend;
        }
    }
    
    public static class Frontend {
        private String dev = "http://localhost:5173";
        private String prod = "https://xiaolidianjing.com";
        
        public String getDev() {
            return dev;
        }
        
        public void setDev(String dev) {
            this.dev = dev;
        }
        
        public String getProd() {
            return prod;
        }
        
        public void setProd(String prod) {
            this.prod = prod;
        }
    }
    
    public static class Backend {
        private String dev = "http://localhost:8080";
        private String prod = "https://api.xiaolidianjing.com";
        
        public String getDev() {
            return dev;
        }
        
        public void setDev(String dev) {
            this.dev = dev;
        }
        
        public String getProd() {
            return prod;
        }
        
        public void setProd(String prod) {
            this.prod = prod;
        }
    }
}
