server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: lian<PERSON>kan-backend

  # 激活的配置文件
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:prod}

  # 通用数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池配置
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:update}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
        # 性能优化
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # JSON配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 通用跨域配置
cors:
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true
