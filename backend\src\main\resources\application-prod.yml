# 生产环境配置
spring:
  # 生产环境数据库配置
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:xiaoli}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=false&verifyServerCertificate=false
    username: ${DB_USERNAME:xiaoli}
    password: ${DB_PASSWORD:hZKdEbZY4RiznYeC}  # 生产环境必须通过环境变量提供密码
    hikari:
      # 生产环境连接池配置（较大）
      maximum-pool-size: 30
      minimum-idle: 10
      idle-timeout: 600000
      connection-timeout: 20000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 连接验证
      connection-test-query: SELECT 1
      validation-timeout: 3000

  # JPA生产环境配置
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境只验证，不自动修改表结构
    show-sql: false       # 生产环境不显示SQL
    properties:
      hibernate:
        format_sql: false
        # 生产环境性能优化
        jdbc:
          batch_size: 50
          fetch_size: 50
        cache:
          use_second_level_cache: false
          use_query_cache: false

# 生产环境域名配置
app:
  domain:
    frontend:
      dev: http://124.223.46.211
      prod: https://xiaolidianjing.com
    backend:
      dev: http://124.223.46.211:8080
      prod: https://xiaolidianjing.com:8080
  environment: prod



# 生产环境日志配置
logging:
  level:
    com.game.lianliankan: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/lianliankan-backend.log
    max-size: 100MB
    max-history: 30

# 生产环境安全配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要的端点
  endpoint:
    health:
      show-details: when-authorized

# 生产环境服务器配置
server:
  # 生产环境错误页面配置
  error:
    include-stacktrace: never
    include-message: never
  # 压缩配置
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
