<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物品选择游戏 - API模拟测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        input {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>物品选择游戏 - API模拟测试</h1>
    
    <div class="test-section">
        <h3>模拟保存和导入流程</h3>
        <p>这个测试模拟了正确的保存导入流程：</p>
        <ol>
            <li>保存：生成短的进度代码（如 B265DC759B8B）</li>
            <li>导入：根据进度代码恢复游戏状态</li>
        </ol>
        
        <button onclick="simulateExport()">模拟保存</button>
        <button onclick="simulateImport()">模拟导入</button>
        
        <div>
            <label>进度代码: </label>
            <input type="text" id="progressCode" placeholder="进度代码" readonly>
            <button onclick="copyCode()">复制</button>
        </div>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 模拟数据库存储
        const mockDatabase = new Map();
        
        // 模拟当前游戏状态
        let currentGameState = {
            playerName: '测试玩家',
            items: [
                { id: 1, name: '实战军靴', imagePath: '/images/item1.png', positionX: 0, positionY: 0, status: 'confirmed' },
                { id: 2, name: 'G.T.I星链套装', imagePath: '/images/item2.png', positionX: 1, positionY: 0, status: 'pending' },
                { id: 3, name: '动力电池组', imagePath: '/images/item3.png', positionX: 2, positionY: 0, status: 'confirmed' },
                { id: 4, name: '非洲之心', imagePath: '/images/item4.png', positionX: 3, positionY: 0, status: 'default' },
                { id: 5, name: '炫彩小箱', imagePath: '/images/item5.png', positionX: 4, positionY: 0, status: 'pending' }
            ]
        };

        function generateProgressCode() {
            // 生成类似 B265DC759B8B 的短代码
            const chars = '0123456789ABCDEF';
            let result = '';
            for (let i = 0; i < 12; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        function simulateExport() {
            try {
                // 模拟后端导出API
                const progressCode = generateProgressCode();
                
                // 保存到模拟数据库
                const gameStateToSave = {
                    playerName: currentGameState.playerName,
                    items: currentGameState.items,
                    timestamp: new Date().toISOString(),
                    version: "1.0",
                    gameType: "item-selection"
                };
                
                mockDatabase.set(progressCode, gameStateToSave);
                
                // 显示结果
                document.getElementById('progressCode').value = progressCode;
                document.getElementById('result').className = 'result success';
                document.getElementById('result').textContent = 
                    `保存成功！\n` +
                    `进度代码: ${progressCode}\n` +
                    `玩家: ${gameStateToSave.playerName}\n` +
                    `已确认物品: ${gameStateToSave.items.filter(item => item.status === 'confirmed').length}\n` +
                    `待确认物品: ${gameStateToSave.items.filter(item => item.status === 'pending').length}\n` +
                    `默认状态物品: ${gameStateToSave.items.filter(item => item.status === 'default').length}\n\n` +
                    `保存的数据:\n${JSON.stringify(gameStateToSave, null, 2)}`;
                
            } catch (error) {
                document.getElementById('result').className = 'result error';
                document.getElementById('result').textContent = '保存失败: ' + error.message;
            }
        }

        function simulateImport() {
            try {
                const progressCode = document.getElementById('progressCode').value.trim();
                if (!progressCode) {
                    throw new Error('请先生成进度代码');
                }
                
                // 模拟后端导入API
                const savedGameState = mockDatabase.get(progressCode);
                if (!savedGameState) {
                    throw new Error('进度码无效或已过期');
                }
                
                // 恢复游戏状态
                currentGameState = {
                    playerName: savedGameState.playerName,
                    items: savedGameState.items.map(item => ({
                        id: item.id,
                        name: item.name,
                        imagePath: item.imagePath,
                        positionX: item.positionX,
                        positionY: item.positionY,
                        status: item.status
                    }))
                };
                
                // 显示结果
                document.getElementById('result').className = 'result success';
                document.getElementById('result').textContent = 
                    `导入成功！\n` +
                    `进度代码: ${progressCode}\n` +
                    `玩家: ${currentGameState.playerName}\n` +
                    `已确认物品: ${currentGameState.items.filter(item => item.status === 'confirmed').length}\n` +
                    `待确认物品: ${currentGameState.items.filter(item => item.status === 'pending').length}\n` +
                    `默认状态物品: ${currentGameState.items.filter(item => item.status === 'default').length}\n\n` +
                    `恢复的数据:\n${JSON.stringify(currentGameState, null, 2)}`;
                
            } catch (error) {
                document.getElementById('result').className = 'result error';
                document.getElementById('result').textContent = '导入失败: ' + error.message;
            }
        }

        function copyCode() {
            const progressCode = document.getElementById('progressCode').value;
            if (progressCode) {
                navigator.clipboard.writeText(progressCode).then(() => {
                    alert('进度代码已复制到剪贴板');
                });
            }
        }

        // 初始化显示当前游戏状态
        document.getElementById('result').textContent = 
            `当前游戏状态:\n` +
            `玩家: ${currentGameState.playerName}\n` +
            `已确认物品: ${currentGameState.items.filter(item => item.status === 'confirmed').length}\n` +
            `待确认物品: ${currentGameState.items.filter(item => item.status === 'pending').length}\n` +
            `默认状态物品: ${currentGameState.items.filter(item => item.status === 'default').length}`;
    </script>
</body>
</html>
