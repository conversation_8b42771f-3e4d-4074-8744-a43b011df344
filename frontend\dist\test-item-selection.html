<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物品选择游戏 - 保存导入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>物品选择游戏 - 保存导入功能测试</h1>
    
    <div class="test-section">
        <h3>1. 创建测试游戏会话</h3>
        <button onclick="createTestSession()">创建测试会话</button>
        <div id="sessionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 模拟游戏状态</h3>
        <button onclick="simulateGameState()">设置测试数据</button>
        <div id="gameStateResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 导出游戏状态</h3>
        <button onclick="exportGameState()" id="exportBtn" disabled>导出游戏状态</button>
        <div id="exportResult" class="result"></div>
        <textarea id="exportCode" placeholder="导出的代码将显示在这里"></textarea>
    </div>

    <div class="test-section">
        <h3>4. 导入游戏状态</h3>
        <textarea id="importCode" placeholder="在这里粘贴要导入的代码"></textarea>
        <button onclick="importGameState()" id="importBtn" disabled>导入游戏状态</button>
        <div id="importResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 验证导入结果</h3>
        <button onclick="verifyImportResult()" id="verifyBtn" disabled>验证导入结果</button>
        <div id="verifyResult" class="result"></div>
    </div>

    <script>
        let sessionId = null;
        let testItems = [];

        async function createTestSession() {
            try {
                const response = await fetch('/api/item-selection/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        playerName: '测试玩家',
                        difficultyLevel: 'normal'
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    sessionId = result.data.sessionId;
                    document.getElementById('sessionResult').className = 'result success';
                    document.getElementById('sessionResult').textContent = 
                        `会话创建成功！\nSessionId: ${sessionId}\n玩家: ${result.data.playerName}`;
                    
                    // 启用其他按钮
                    document.getElementById('exportBtn').disabled = false;
                    document.getElementById('importBtn').disabled = false;
                    document.getElementById('verifyBtn').disabled = false;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('sessionResult').className = 'result error';
                document.getElementById('sessionResult').textContent = '创建会话失败: ' + error.message;
            }
        }

        async function simulateGameState() {
            if (!sessionId) {
                alert('请先创建测试会话');
                return;
            }

            // 模拟一些物品状态：一些confirmed，一些pending，一些default
            testItems = [
                { id: 1, name: '实战军靴', imagePath: '/images/item1.png', positionX: 0, positionY: 0, status: 'confirmed' },
                { id: 2, name: 'G.T.I星链套装', imagePath: '/images/item2.png', positionX: 1, positionY: 0, status: 'pending' },
                { id: 3, name: '动力电池组', imagePath: '/images/item3.png', positionX: 2, positionY: 0, status: 'confirmed' },
                { id: 4, name: '非洲之心', imagePath: '/images/item4.png', positionX: 3, positionY: 0, status: 'default' },
                { id: 5, name: '炫彩小箱', imagePath: '/images/item5.png', positionX: 4, positionY: 0, status: 'pending' }
            ];

            try {
                const response = await fetch('/api/item-selection/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        items: testItems
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    document.getElementById('gameStateResult').className = 'result success';
                    document.getElementById('gameStateResult').textContent = 
                        `测试数据设置成功！\n` +
                        `已确认物品: ${testItems.filter(item => item.status === 'confirmed').length}\n` +
                        `待确认物品: ${testItems.filter(item => item.status === 'pending').length}\n` +
                        `默认状态物品: ${testItems.filter(item => item.status === 'default').length}`;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('gameStateResult').className = 'result error';
                document.getElementById('gameStateResult').textContent = '设置测试数据失败: ' + error.message;
            }
        }

        async function exportGameState() {
            if (!sessionId) {
                alert('请先创建测试会话');
                return;
            }

            try {
                const response = await fetch('/api/item-selection/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        items: testItems,
                        playerName: '测试玩家'
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    const exportCode = result.data.exportCode;
                    document.getElementById('exportCode').value = exportCode;
                    document.getElementById('importCode').value = exportCode; // 自动填入导入框
                    
                    document.getElementById('exportResult').className = 'result success';
                    document.getElementById('exportResult').textContent = 
                        `导出成功！\n` +
                        `进度代码: ${exportCode}\n` +
                        `玩家: ${result.data.playerName}\n` +
                        `待确认: ${result.data.pendingCount}\n` +
                        `已确认: ${result.data.confirmedCount}`;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('exportResult').className = 'result error';
                document.getElementById('exportResult').textContent = '导出失败: ' + error.message;
            }
        }

        async function importGameState() {
            if (!sessionId) {
                alert('请先创建测试会话');
                return;
            }

            const importCode = document.getElementById('importCode').value.trim();
            if (!importCode) {
                alert('请输入导入代码');
                return;
            }

            try {
                const response = await fetch('/api/item-selection/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        importCode: importCode
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    document.getElementById('importResult').className = 'result success';
                    document.getElementById('importResult').textContent = '导入成功！';
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('importResult').className = 'result error';
                document.getElementById('importResult').textContent = '导入失败: ' + error.message;
            }
        }

        async function verifyImportResult() {
            if (!sessionId) {
                alert('请先创建测试会话');
                return;
            }

            try {
                const response = await fetch(`/api/item-selection/${sessionId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                if (result.code === 200) {
                    const gameState = result.data;
                    const items = gameState.items || [];
                    
                    const confirmedCount = items.filter(item => item.status === 'confirmed').length;
                    const pendingCount = items.filter(item => item.status === 'pending').length;
                    const defaultCount = items.filter(item => item.status === 'default').length;
                    
                    document.getElementById('verifyResult').className = 'result success';
                    document.getElementById('verifyResult').textContent = 
                        `验证结果：\n` +
                        `玩家: ${gameState.playerName}\n` +
                        `总物品数: ${items.length}\n` +
                        `已确认物品: ${confirmedCount}\n` +
                        `待确认物品: ${pendingCount}\n` +
                        `默认状态物品: ${defaultCount}\n\n` +
                        `物品详情:\n` +
                        items.map(item => `${item.name}: ${item.status}`).join('\n');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                document.getElementById('verifyResult').className = 'result error';
                document.getElementById('verifyResult').textContent = '验证失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
